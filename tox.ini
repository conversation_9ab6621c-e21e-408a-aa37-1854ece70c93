[tox]
requires =
    tox>=4
env_list = py{38,39,310,311,312},docs,pre-commit

[gh-actions]
python =
    3.8: py38
    3.9: py39
    3.10: py310
    3.11: py311
    3.12: py312, docs

[testenv]
description = run tests
deps =
    pytest
commands =
    pytest

[testenv:docs]
description = run doc snippets
base_python = 3.12
setenv = PY_IGNORE_IMPORTMISMATCH=1
deps =
    pytest
    pytest-markdown-docs
commands =
    pytest -vv --markdown-docs parmancer examples README.md

[testenv:pre-commit]
skip_install = true
deps = pre-commit
commands = pre-commit run --all-files --show-diff-on-failure
